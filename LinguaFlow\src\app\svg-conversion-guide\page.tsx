"use client";

import { useState } from 'react';
import { CustomLogo } from '@/components/icons/custom-logo';

export default function SVGConversionGuide() {
  const [currentStep, setCurrentStep] = useState(1);
  const [svgCode, setSvgCode] = useState('');

  const steps = [
    {
      title: "Prepare Your Logo Image",
      content: (
        <div className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Image Requirements:</h4>
            <ul className="list-disc list-inside space-y-1 text-blue-700 dark:text-blue-300">
              <li>High resolution (minimum 512x512px)</li>
              <li>PNG format with transparent background preferred</li>
              <li>Clean, sharp edges</li>
              <li>Solid colors (your logo has beautiful blues!)</li>
            </ul>
          </div>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Your Logo Colors:</h4>
            <div className="flex gap-4">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-400 rounded border"></div>
                <span className="text-sm">#5DADE2 (Light Blue)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-600 rounded border"></div>
                <span className="text-sm">#3498DB (Blue)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-white rounded border border-gray-300"></div>
                <span className="text-sm">#FFFFFF (White)</span>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Convert to SVG using Vectorizer.io",
      content: (
        <div className="space-y-4">
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">Step-by-Step:</h4>
            <ol className="list-decimal list-inside space-y-2 text-green-700 dark:text-green-300">
              <li>Go to <a href="https://vectorizer.io" target="_blank" className="underline font-medium">vectorizer.io</a></li>
              <li>Click "Upload Image" and select your logo</li>
              <li>Wait for processing (30-60 seconds)</li>
              <li>Preview the result - check colors and shapes</li>
              <li>Click "Download SVG" if it looks good</li>
            </ol>
          </div>
          <div className="flex justify-center">
            <a 
              href="https://vectorizer.io" 
              target="_blank" 
              rel="noopener noreferrer"
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              🚀 Open Vectorizer.io
            </a>
          </div>
        </div>
      )
    },
    {
      title: "Extract SVG Code",
      content: (
        <div className="space-y-4">
          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">Extract the Code:</h4>
            <ol className="list-decimal list-inside space-y-2 text-purple-700 dark:text-purple-300">
              <li>Open the downloaded SVG file in a text editor (Notepad, VS Code, etc.)</li>
              <li>Copy everything between <code>&lt;svg&gt;</code> and <code>&lt;/svg&gt;</code> tags</li>
              <li>Paste the code in the textarea below to preview</li>
            </ol>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Paste your SVG code here:</label>
            <textarea
              value={svgCode}
              onChange={(e) => setSvgCode(e.target.value)}
              className="w-full h-32 p-3 border rounded-lg font-mono text-sm"
              placeholder="<svg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'>
  <!-- Your logo paths will appear here -->
</svg>"
            />
          </div>
          {svgCode && (
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Preview:</h4>
              <div 
                className="w-16 h-16 border rounded"
                dangerouslySetInnerHTML={{ __html: svgCode }}
              />
            </div>
          )}
        </div>
      )
    },
    {
      title: "Optimize and Clean Up",
      content: (
        <div className="space-y-4">
          <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-800 dark:text-orange-200 mb-2">Clean Up Checklist:</h4>
            <ul className="list-disc list-inside space-y-1 text-orange-700 dark:text-orange-300">
              <li>Remove unnecessary attributes (id, class, style if not needed)</li>
              <li>Simplify complex paths if possible</li>
              <li>Add unique IDs with "linguaflow-" prefix</li>
              <li>Ensure colors match your brand</li>
              <li>Test at small sizes (16x16px)</li>
            </ul>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Example Clean SVG Structure:</h4>
            <pre className="text-sm overflow-x-auto">
{`<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="linguaflow-gradient">
      <stop stopColor="#5DADE2" />
      <stop offset="1" stopColor="#3498DB" />
    </linearGradient>
  </defs>
  <path d="..." fill="url(#linguaflow-gradient)" />
  <path d="..." fill="white" />
</svg>`}
            </pre>
          </div>
        </div>
      )
    },
    {
      title: "Implement in Component",
      content: (
        <div className="space-y-4">
          <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-indigo-800 dark:text-indigo-200 mb-2">Final Steps:</h4>
            <ol className="list-decimal list-inside space-y-2 text-indigo-700 dark:text-indigo-300">
              <li>Open <code>src/components/icons/custom-logo.tsx</code></li>
              <li>Replace the placeholder content between the comments</li>
              <li>Update the viewBox to match your logo dimensions</li>
              <li>Test using the logo test page</li>
            </ol>
          </div>
          <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Test Your Logo:</h4>
            <div className="flex gap-4 items-center">
              <CustomLogo size={16} />
              <CustomLogo size={32} />
              <CustomLogo size={48} />
              <CustomLogo size={64} />
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              Current logo (will update when you replace the SVG)
            </p>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800 dark:text-white">
          SVG Conversion Guide
        </h1>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Progress</span>
            <span className="text-sm text-gray-600 dark:text-gray-400">{currentStep} of {steps.length}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / steps.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Step Navigation */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          {steps.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentStep(index + 1)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                currentStep === index + 1
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Step {index + 1}
            </button>
          ))}
        </div>

        {/* Current Step Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
          <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
            Step {currentStep}: {steps[currentStep - 1].title}
          </h2>
          {steps[currentStep - 1].content}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <button
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className="px-6 py-3 bg-gray-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}
            disabled={currentStep === steps.length}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
          >
            Next
          </button>
        </div>

        {/* Quick Links */}
        <div className="mt-8 text-center space-x-4">
          <a 
            href="/logo-test" 
            className="inline-block bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors"
          >
            🧪 Test Logo
          </a>
          <a 
            href="/" 
            className="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
          >
            🏠 Back to App
          </a>
        </div>
      </div>
    </div>
  );
}
