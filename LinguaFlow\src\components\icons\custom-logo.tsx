import type { SVGProps } from 'react';

interface CustomLogoProps extends SVGProps<SVGSVGElement> {
  variant?: 'light' | 'dark';
  size?: number;
  optimizeForSize?: boolean; // New prop for size-specific optimization
}

export function CustomLogo({
  variant = 'light',
  size,
  optimizeForSize = true,
  ...props
}: CustomLogoProps) {
  const actualSize = size || 100;
  const isSmall = actualSize <= 24;
  const isTiny = actualSize <= 16;

  // Adjust stroke width based on size
  const getStrokeWidth = (baseWidth: number) => {
    if (!optimizeForSize) return baseWidth;
    if (isTiny) return Math.max(baseWidth * 2, 2);
    if (isSmall) return Math.max(baseWidth * 1.5, 1.5);
    return baseWidth;
  };

  return (
    <svg
      width={actualSize}
      height={actualSize}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg" version="1.2" baseProfile="tiny-ps" width="640px" height="641px" viewBox="0 0 640 641" preserveAspectRatio="xMidYMid meet">
 <g fill="#f9fcfd">
  <defs>
    <linearGradient
      id="linguaflow-main-gradient"
      x1="0%"
      y1="0%"
      x2="  0%"
      y2="100%"
      gradientUnits="userSpaceOnUse"
    >
      <stop stopColor="#5DADE2" />
      <stop offset="1" stopColor="#3498DB" />
    </linearGradient>
  </defs>
 </g>
      {...props}
      {/*
        STEP-BY-STEP REPLACEMENT GUIDE:

        🎯 QUICK START:
        1. Go to https://vectorizer.io
        2. Upload your logo image
        3. Download the SVG
        4. Copy the content between <svg> tags
        5. Replace the PLACEHOLDER section below<svg xmlns="http://www.w3.org/2000/svg" version="1.2" baseProfile="tiny-ps" width="640px" height="641px" viewBox="0 0 640 641" preserveAspectRatio="xMidYMid meet">
 <g fill="#f9fcfd">

        🎨 YOUR LOGO COLORS (from the image you showed):
        - Light Blue: #5DADE2 or #6BB6FF
        - Medium Blue: #3498DB or #4A90E2
        - Dark Blue: #2980B9 (for borders)
        - White: #FFFFFF (for contrast)

        🔧 OPTIMIZATION TIPS:
        - The component automatically adjusts stroke widths for small sizes
        - Use the optimizeForSize prop to enable/disable this
        - Test at 16px size (favicon) to ensure visibility

        📝 IMPLEMENTATION CHECKLIST:
        □ Convert logo to SVG
        □ Replace placeholder content below
        □ Update viewBox if needed
        □ Add "linguaflow-" prefix to all IDs
        □ Test at different sizes
        □ Generate favicon files
      */}

      {"C:\Users\<USER>\Desktop\FreeSample-Vectorizer-io-cropped_circle_image.svg"}
      <defs>
        <linearGradient
          id="linguaflow-main-gradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5DADE2" />
          <stop offset="1" stopColor="#3498DB" />
        </linearGradient>
        <linearGradient
          id="linguaflow-accent-gradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#6BB6FF" />
          <stop offset="1" stopColor="#4A90E2" />
        </linearGradient>
      </defs>

      {/* Background circle with size-optimized stroke */}
      <circle
        cx="50"
        cy="50"
        r="46"
        fill="url(#linguaflow-main-gradient)"
        stroke={variant === 'dark' ? '#ffffff' : '#2980B9'}
        strokeWidth={getStrokeWidth(2)}
      />

      {/* Simplified L shape - optimized for small sizes */}
      <path
        d="M25 25 L25 70 L60 70"
        stroke="white"
        strokeWidth={getStrokeWidth(isTiny ? 4 : 6)}
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />

      {/* Simplified feather/wing element */}
      <path
        d="M45 25 C 55 35, 65 40, 65 50 C 65 60, 55 65, 45 75"
        fill="white"
        opacity={isTiny ? 0.9 : 1}
      />

      {/* Optional detail lines - hidden at very small sizes */}
      {!isTiny && (
        <g stroke="url(#linguaflow-accent-gradient)" strokeWidth={getStrokeWidth(1.5)} strokeLinecap="round">
          <line x1="50" y1="40" x2="60" y2="40" />
          <line x1="50" y1="50" x2="65" y2="50" />
          <line x1="50" y1="60" x2="60" y2="60" />
        </g>
      )}

      {/* END PLACEHOLDER */}
    </svg>
  );
}

// Export a simplified version specifically for favicons
export function FaviconLogo(props: SVGProps<SVGSVGElement>) {
  return (
    <CustomLogo
      {...props}
      size={16}
      optimizeForSize={true}
      className="favicon-logo"
    />
  );
}
