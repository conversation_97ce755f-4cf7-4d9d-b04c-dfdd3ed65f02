/**
 * SVG Optimization Helper
 * 
 * This script provides guidance for optimizing your SVG for small sizes
 */

const optimizationRules = {
  strokeWidth: {
    name: "Stroke Width Optimization",
    check: (svgContent) => {
      const strokeWidthMatches = svgContent.match(/stroke-width="([^"]+)"/g) || [];
      const thinStrokes = strokeWidthMatches.filter(match => {
        const width = parseFloat(match.match(/stroke-width="([^"]+)"/)[1]);
        return width < 2;
      });
      return {
        passed: thinStrokes.length === 0,
        issues: thinStrokes.length,
        recommendation: thinStrokes.length > 0 ? 
          "Increase stroke widths to at least 2px for better visibility at small sizes" : 
          "Stroke widths look good for small sizes"
      };
    }
  },
  
  fontSize: {
    name: "Font Size Check",
    check: (svgContent) => {
      const fontSizeMatches = svgContent.match(/font-size="([^"]+)"/g) || [];
      const smallFonts = fontSizeMatches.filter(match => {
        const size = parseFloat(match.match(/font-size="([^"]+)"/)[1]);
        return size < 8;
      });
      return {
        passed: smallFonts.length === 0,
        issues: smallFonts.length,
        recommendation: smallFonts.length > 0 ? 
          "Remove or increase font sizes smaller than 8px - they won't be readable at small sizes" : 
          "Font sizes are appropriate for small displays"
      };
    }
  },
  
  complexity: {
    name: "Path Complexity",
    check: (svgContent) => {
      const pathMatches = svgContent.match(/<path[^>]*d="([^"]+)"/g) || [];
      const complexPaths = pathMatches.filter(match => {
        const pathData = match.match(/d="([^"]+)"/)[1];
        // Count the number of path commands (rough complexity measure)
        const commands = pathData.match(/[MLHVCSQTAZ]/gi) || [];
        return commands.length > 20;
      });
      return {
        passed: complexPaths.length === 0,
        issues: complexPaths.length,
        recommendation: complexPaths.length > 0 ? 
          "Consider simplifying complex paths - they may not render well at small sizes" : 
          "Path complexity is reasonable for small sizes"
      };
    }
  },
  
  viewBox: {
    name: "ViewBox Optimization",
    check: (svgContent) => {
      const viewBoxMatch = svgContent.match(/viewBox="([^"]+)"/);
      if (!viewBoxMatch) {
        return {
          passed: false,
          issues: 1,
          recommendation: "Add a viewBox attribute for proper scaling"
        };
      }
      
      const viewBoxValues = viewBoxMatch[1].split(/\s+/).map(Number);
      const [x, y, width, height] = viewBoxValues;
      
      // Check if viewBox starts at 0,0 (recommended)
      const startsAtOrigin = x === 0 && y === 0;
      
      return {
        passed: startsAtOrigin,
        issues: startsAtOrigin ? 0 : 1,
        recommendation: startsAtOrigin ? 
          "ViewBox is properly configured" : 
          "Consider adjusting viewBox to start at 0,0 for easier scaling"
      };
    }
  },
  
  uniqueIds: {
    name: "Unique ID Check",
    check: (svgContent) => {
      const idMatches = svgContent.match(/id="([^"]+)"/g) || [];
      const ids = idMatches.map(match => match.match(/id="([^"]+)"/)[1]);
      const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
      const uniqueDuplicates = [...new Set(duplicates)];
      
      return {
        passed: uniqueDuplicates.length === 0,
        issues: uniqueDuplicates.length,
        recommendation: uniqueDuplicates.length > 0 ? 
          `Duplicate IDs found: ${uniqueDuplicates.join(', ')}. Add 'linguaflow-' prefix to make them unique` : 
          "All IDs are unique"
      };
    }
  }
};

function analyzeSVG(svgContent) {
  console.log('🔍 SVG OPTIMIZATION ANALYSIS');
  console.log('='.repeat(50));
  
  const results = {};
  let totalIssues = 0;
  
  Object.entries(optimizationRules).forEach(([key, rule]) => {
    const result = rule.check(svgContent);
    results[key] = result;
    totalIssues += result.issues;
    
    const status = result.passed ? '✅' : '⚠️';
    console.log(`\n${status} ${rule.name}`);
    console.log(`   ${result.recommendation}`);
    if (result.issues > 0) {
      console.log(`   Issues found: ${result.issues}`);
    }
  });
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 SUMMARY: ${totalIssues === 0 ? 'All checks passed!' : `${totalIssues} issues found`}`);
  
  if (totalIssues > 0) {
    console.log('\n🛠️  OPTIMIZATION SUGGESTIONS:');
    console.log('1. Use stroke-width of at least 2px');
    console.log('2. Remove text smaller than 8px');
    console.log('3. Simplify complex paths');
    console.log('4. Add "linguaflow-" prefix to all IDs');
    console.log('5. Ensure viewBox starts at 0,0');
  }
  
  return results;
}

// Example usage and templates
console.log('🎨 SVG OPTIMIZATION HELPER');
console.log('='.repeat(50));

console.log('\n📋 OPTIMIZATION CHECKLIST:');
console.log('□ Stroke width ≥ 2px');
console.log('□ No text smaller than 8px');
console.log('□ Simplified paths');
console.log('□ Unique IDs with "linguaflow-" prefix');
console.log('□ ViewBox starts at 0,0');
console.log('□ High contrast colors');
console.log('□ Tested at 16x16px size');

console.log('\n🎯 OPTIMIZED SVG TEMPLATE:');
console.log(`
<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="linguaflow-main-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop stopColor="#5DADE2" />
      <stop offset="1" stopColor="#3498DB" />
    </linearGradient>
  </defs>
  
  <!-- Background circle with thick border -->
  <circle 
    cx="50" 
    cy="50" 
    r="45" 
    fill="url(#linguaflow-main-gradient)" 
    stroke="#2980B9" 
    strokeWidth="3"
  />
  
  <!-- Simplified logo elements with thick strokes -->
  <path 
    d="M25 25 L25 75 L60 75" 
    stroke="white" 
    strokeWidth="6" 
    strokeLinecap="round" 
    fill="none"
  />
  
  <!-- Simplified feather/wing element -->
  <path 
    d="M45 25 C 55 35, 65 40, 65 50 C 65 60, 55 65, 45 75" 
    fill="white"
  />
</svg>
`);

console.log('\n🧪 TO TEST YOUR SVG:');
console.log('1. Copy your SVG code');
console.log('2. Replace the example above with your code');
console.log('3. Run: analyzeSVG(yourSvgString)');
console.log('4. Follow the optimization suggestions');

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { analyzeSVG, optimizationRules };
}
