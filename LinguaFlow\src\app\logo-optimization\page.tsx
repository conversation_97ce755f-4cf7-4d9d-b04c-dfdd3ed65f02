"use client";

import { useState } from 'react';
import { CustomLogo } from '@/components/icons/custom-logo';

export default function LogoOptimization() {
  const [selectedSize, setSelectedSize] = useState(16);

  const optimizationTips = [
    {
      title: "Simplify Complex Elements",
      description: "Remove fine details that won't be visible at small sizes",
      example: "Your feather details might need to be simplified to bold strokes",
      priority: "High"
    },
    {
      title: "Increase Stroke Width",
      description: "Make lines thicker so they're visible at small sizes",
      example: "Minimum 2px stroke width for 16x16 size",
      priority: "High"
    },
    {
      title: "Use High Contrast Colors",
      description: "Ensure good contrast between elements",
      example: "Your blue (#5DADE2) on white background has good contrast",
      priority: "Medium"
    },
    {
      title: "Remove Small Text",
      description: "Text smaller than 8px becomes unreadable",
      example: "Focus on the 'L' and 'E' shapes rather than full text",
      priority: "High"
    },
    {
      title: "Merge Close Elements",
      description: "Combine elements that are too close together",
      example: "Merge overlapping paths into single shapes",
      priority: "Medium"
    },
    {
      title: "Test at Target Size",
      description: "Always test your logo at the actual size it will be used",
      example: "16x16px for favicon, 32x32px for app icons",
      priority: "Critical"
    }
  ];

  const sizeTests = [
    { size: 16, label: "Favicon", description: "Browser tab icon" },
    { size: 24, label: "Small", description: "Mobile navigation" },
    { size: 32, label: "Medium", description: "Desktop navigation" },
    { size: 48, label: "Large", description: "App headers" },
    { size: 64, label: "XL", description: "Feature displays" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800 dark:text-white">
          Logo Optimization for Small Sizes
        </h1>

        {/* Size Testing Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
              Size Testing
            </h2>
            <div className="space-y-4">
              {sizeTests.map((test) => (
                <div 
                  key={test.size}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedSize === test.size 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedSize(test.size)}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      <CustomLogo size={test.size} />
                    </div>
                    <div>
                      <h3 className="font-semibold">{test.label} ({test.size}px)</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{test.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-2xl font-bold mb-4 text-gray-800 dark:text-white">
              Detailed View: {selectedSize}px
            </h2>
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="relative">
                  <CustomLogo size={selectedSize * 4} className="border border-gray-300 rounded" />
                  <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">
                    4x magnified view
                  </div>
                </div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">Actual Size:</h4>
                <div className="flex items-center gap-4">
                  <CustomLogo size={selectedSize} className="border border-gray-400" />
                  <span className="text-sm">This is how users will see it</span>
                </div>
              </div>
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                  Optimization Notes for {selectedSize}px:
                </h4>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  {selectedSize <= 16 && (
                    <>
                      <li>• Remove all fine details</li>
                      <li>• Use minimum 2px stroke width</li>
                      <li>• Focus on main shape only</li>
                    </>
                  )}
                  {selectedSize > 16 && selectedSize <= 32 && (
                    <>
                      <li>• Simplify complex elements</li>
                      <li>• Maintain key brand elements</li>
                      <li>• Ensure good contrast</li>
                    </>
                  )}
                  {selectedSize > 32 && (
                    <>
                      <li>• Most details can be preserved</li>
                      <li>• Good size for full logo display</li>
                      <li>• Test readability of text elements</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Optimization Tips */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
            Optimization Guidelines
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {optimizationTips.map((tip, index) => (
              <div 
                key={index}
                className={`p-4 rounded-lg border-l-4 ${
                  tip.priority === 'Critical' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
                  tip.priority === 'High' ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' :
                  'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-800 dark:text-white">{tip.title}</h3>
                  <span className={`text-xs px-2 py-1 rounded ${
                    tip.priority === 'Critical' ? 'bg-red-200 text-red-800' :
                    tip.priority === 'High' ? 'bg-orange-200 text-orange-800' :
                    'bg-blue-200 text-blue-800'
                  }`}>
                    {tip.priority}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{tip.description}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 italic">{tip.example}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Before/After Comparison */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
            Before vs After Optimization
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="text-center">
              <h3 className="font-semibold mb-4 text-red-600">❌ Before (Complex)</h3>
              <div className="bg-gray-100 dark:bg-gray-700 p-8 rounded-lg">
                <div className="text-xs text-gray-500 mb-2">Simulated complex logo</div>
                <svg width="64" height="64" viewBox="0 0 64 64" className="mx-auto">
                  <circle cx="32" cy="32" r="30" fill="#5DADE2" stroke="#3498DB" strokeWidth="0.5"/>
                  <path d="M20 20 L20 44 L35 44" stroke="white" strokeWidth="1" fill="none"/>
                  <path d="M30 20 C 35 22, 45 25, 45 32 C 45 39, 35 42, 30 44" fill="white"/>
                  <path d="M35 28 L42 28 M35 32 L45 32 M35 36 L42 36" stroke="#5DADE2" strokeWidth="0.5"/>
                  <text x="32" y="52" textAnchor="middle" fill="white" fontSize="4">LinguaFlow</text>
                </svg>
                <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                  <p>❌ Thin strokes (0.5px)</p>
                  <p>❌ Small text (4px)</p>
                  <p>❌ Too many details</p>
                </div>
              </div>
            </div>
            <div className="text-center">
              <h3 className="font-semibold mb-4 text-green-600">✅ After (Optimized)</h3>
              <div className="bg-gray-100 dark:bg-gray-700 p-8 rounded-lg">
                <div className="text-xs text-gray-500 mb-2">Optimized for small sizes</div>
                <svg width="64" height="64" viewBox="0 0 64 64" className="mx-auto">
                  <circle cx="32" cy="32" r="30" fill="#5DADE2" stroke="#3498DB" strokeWidth="2"/>
                  <path d="M22 22 L22 42 L38 42" stroke="white" strokeWidth="4" fill="none" strokeLinecap="round"/>
                  <path d="M30 22 C 36 26, 42 28, 42 32 C 42 36, 36 38, 30 42" fill="white"/>
                </svg>
                <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                  <p>✅ Bold strokes (4px)</p>
                  <p>✅ No small text</p>
                  <p>✅ Simplified shapes</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Practical Steps */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-6 shadow-lg">
          <h2 className="text-2xl font-bold mb-4">Ready to Optimize Your Logo?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/10 rounded-lg p-4">
              <h3 className="font-semibold mb-2">1. Create Simplified Version</h3>
              <p className="text-sm opacity-90">Make a version with thicker strokes and fewer details for small sizes</p>
            </div>
            <div className="bg-white/10 rounded-lg p-4">
              <h3 className="font-semibold mb-2">2. Test at Target Sizes</h3>
              <p className="text-sm opacity-90">Use the size testing tool above to verify readability</p>
            </div>
            <div className="bg-white/10 rounded-lg p-4">
              <h3 className="font-semibold mb-2">3. Create Multiple Versions</h3>
              <p className="text-sm opacity-90">Consider different versions for different size ranges</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center space-x-4">
          <a 
            href="/svg-conversion-guide" 
            className="inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
          >
            📝 SVG Guide
          </a>
          <a 
            href="/logo-test" 
            className="inline-block bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors"
          >
            🧪 Test Logo
          </a>
          <a 
            href="/" 
            className="inline-block bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
          >
            🏠 Back to App
          </a>
        </div>
      </div>
    </div>
  );
}
