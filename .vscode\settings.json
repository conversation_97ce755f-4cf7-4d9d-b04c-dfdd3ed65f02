{"python.analysis.typeCheckingMode": "basic", "cmake.configureArgs": [], "C_Cpp.errorSquiggles": "disabled", "cSpell.words": ["Acciones", "Aleykum", "anında", "Aracı", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Casualize", "commuincation", "De<PERSON>ch", "Diacritization", "diacritized", "Diacritizing", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Escritura", "Español", "firestore", "Français", "genkit", "go<PERSON><PERSON><PERSON>", "Gramma", "grammarpro", "gweight", "hackathons", "Herramientas", "<PERSON><PERSON><PERSON>", "Italiano", "iterature", "jsonify", "Kontrol", "kruskal", "lateinit", "linguaflow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "mstidx", "Nederlands", "nextn", "nums", "pakage", "pglite", "pnode", "Poping", "Primsalgorithm", "Prototip", "rephraser", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "sonner", "Stil", "<PERSON><PERSON><PERSON><PERSON>", "Subir", "<PERSON><PERSON><PERSON><PERSON>", "tedge", "tnode", "tooltiptext", "turbopack", "Türkçe", "tweight", "Vectorizer", "vercel", "veya", "yapıştırın", "yazın", "أثناء", "أ<PERSON><PERSON><PERSON>", "أداة", "أدناه", "أدوات", "أسفل", "أسلوب", "أسلوبي", "أكاديمي", "أكثر", "<PERSON>و<PERSON>ه", "إبداعي", "إحصائيات", "إدخال", "إدخالك", "إدراج", "إعادة", "إعدادات", "إلحاق", "إلغاء", "إملائي", "إنشاء", "إنشاءات", "إنشاؤه", "ابد<PERSON>", "احترافي", "احتمالية", "احصل", "احمِ", "اختر", "استخدام", "استعادة", "استمر", "اقتراح", "اقتراحات", "اكتب", "اكتمل", "الأحرف", "الأدبية", "الأصالة", "الأصلي", "الإدخال", "الإعدادات", "الإنشاء", "الاختيار", "الاصطناعي", "الاقتراح", "البشرية", "التبديل", "التحديد", "التحليل", "التشابه", "الثقة", "الحافظة", "الحصول", "الحفاظ", "الحقوق", "الخاص", "الخاصة", "الداكن", "الذكاء", "الذي", "الرسمية", "السرقة", "السعودية", "السورية", "الشرح", "الصق", "الصوت", "الصياغة", "الطلب", "العثور", "العربية", "الفاتح", "القائمة", "الكبيرة", "الكتابة", "الكلمات", "اللغة", "المؤلفات", "المتصفح", "المحاولة", "المحتوى", "المحدد", "المحرر", "المحول", "المصرية", "المصممة", "المظهر", "المقصودة", "المكتوب", "الملاحظات", "الملف", "المنسدلة", "المُنشأ", "المهندس", "الموجودة", "الموسيقى", "الميزة", "الناتج", "النبرة", "النص", "النصوص", "الوضع", "انتظر", "انقر", "بإنشاء", "باستخدام", "بالذكاء", "بالكامل", "بالكتابة", "بالمحرر", "بتحليل", "بتخصيص", "بحثًا", "بدقة", "بشرية", "بناءً", "بنجاح", "بواسطة", "تأليف", "تبديل", "تجربتك", "تحديد", "تحليل", "تحميل", "تحويل", "تسجيل", "تطبيق", "تعذر", "تغير", "تقرير", "تقني", "تلقائيًا", "توجد", "جا<PERSON>ز", "جميع", "حاول", "درجة", "دعمكم", "راجع", "راجعها", "ربما", "رسمي", "رسمية", "روبوت", "شبهاً", "شكركم", "شكليات", "صياغة", "صياغته", "طلبك", "عملك", "فارغ", "فورية", "فوقه", "قدّر", "قراءة", "قصيرة", "كتاباتك", "كتابة", "كتابتك", "كشّاف", "لإزالة", "لإعادة", "لإملاء", "لإنشاء", "لتحليل", "لتحليلها", "لتحميل", "لتحويله", "لرؤيتها", "لصياغات", "لف<PERSON>ص", "لقبول", "للتحقق", "للتوقف", "للحصول", "للنص", "لنسخ", "لوحة", "ليبدو", "مباشرة", "مثال", "مح<PERSON><PERSON><PERSON>", "محفوظة", "مختلفة", "مدعوم", "مدعومة", "مساعدة", "مطلوب", "معلومة", "ملاحظات", "ملخص", "منطقة", "مو<PERSON><PERSON>", "نبرة", "نبرته", "نجاح", "نحوي", "نزاهتك", "نصًا", "نقدر", "وإلحاق", "واقتراحات", "والوضوح", "وثقة", "ونبرة", "يؤثر", "ير<PERSON>ى", "يساعدك", "يقترح", "يقوم", "يكتشف", "يكون", "يمكن", "يو<PERSON>د", "يوفر"], "cSpell.language": "en,en-US", "python.testing.unittestArgs": ["-v", "-s", "./Python Projects", "-p", "*test.py"], "python.testing.pytestEnabled": false, "python.testing.unittestEnabled": true, "Codegeex.SidebarUI.LanguagePreference": "English", "Codegeex.Chat.LanguagePreference": "English", "Codegeex.Comment.LanguagePreference": "English", "Codegeex.CommitMessage.LanguagePreference": "English", "Codegeex.RepoIndex": true}